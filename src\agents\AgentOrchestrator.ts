import fs from 'fs-extra';
import path from 'path';
import { <PERSON><PERSON><PERSON>ider, AgentContext, Message, ToolCall, ToolResult, LLMResponse } from '../types';
import { Logger } from '../utils/Logger';
import { ConfigManager } from '../config/ConfigManager';
import { LLMProviderFactory } from '../providers/LLMProviderFactory';
import { ToolRegistry } from '../tools/ToolRegistry';
import { SessionManager } from '../session/SessionManager';
import { ContextEngine } from '../context/ContextEngine';
import { LLMProviderError } from '../utils/ErrorHandler';

export class AgentOrchestrator {
  private static instance: AgentOrchestrator;
  private logger: Logger;
  private configManager: ConfigManager;
  private llmProviderFactory: LLMProviderFactory;
  private toolRegistry: ToolRegistry;
  private sessionManager: SessionManager;
  private contextEngine: ContextEngine;
  private currentProvider: LLMProvider | null;

  private constructor() {
    this.logger = Logger.getInstance();
    this.configManager = ConfigManager.getInstance();
    this.llmProviderFactory = LLMProviderFactory.getInstance();
    this.toolRegistry = ToolRegistry.getInstance();
    this.sessionManager = SessionManager.getInstance();
    this.contextEngine = ContextEngine.getInstance();
    this.currentProvider = null;
  }

  public static getInstance(): AgentOrchestrator {
    if (!AgentOrchestrator.instance) {
      AgentOrchestrator.instance = new AgentOrchestrator();
    }
    return AgentOrchestrator.instance;
  }

  public async initialize(workingDirectory: string): Promise<AgentContext> {
    try {
      this.logger.info('Initializing Agent Orchestrator', { workingDirectory });

      // Initialize LLM provider
      const config = this.configManager.getAgentConfig();
      this.currentProvider = await this.llmProviderFactory.getProvider(config.provider);

      // Create agent context
      const sessionId = 'session-' + Date.now();
      const agentContext = await this.contextEngine.createAgentContext(sessionId, workingDirectory);

      // Create and set session
      const session = await this.sessionManager.createSession(workingDirectory, agentContext);
      this.logger.setSessionId(session.id);

      this.logger.info('Agent Orchestrator initialized successfully', {
        sessionId: session.id,
        provider: config.provider,
        model: config.model,
        projectType: agentContext.projectContext.projectType
      });

      return agentContext;

    } catch (error) {
      this.logger.error('Failed to initialize Agent Orchestrator', {
        error: (error as Error).message,
        workingDirectory
      });
      throw error;
    }
  }

  public async processUserInput(
    input: string,
    context: AgentContext,
    options: {
      streaming?: boolean;
      maxIterations?: number;
      enableToolCalling?: boolean;
    } = {}
  ): Promise<string> {
    const maxIterations = options.maxIterations || 5;
    const enableToolCalling = options.enableToolCalling ?? this.configManager.getAgentConfig().enableToolCalling;
    
    try {
      this.logger.info('Processing user input', {
        inputLength: input.length,
        sessionId: context.sessionId,
        streaming: options.streaming,
        enableToolCalling
      });

      // Add user message to conversation history
      const userMessage: Message = {
        role: 'user',
        content: input
      };
      this.sessionManager.addMessage(userMessage);

      let response = '';
      let iteration = 0;

      while (iteration < maxIterations) {
        iteration++;

        this.logger.debug(`Agent iteration ${iteration}/${maxIterations}`);

        // Get LLM response
        const llmResponse = await this.getLLMResponse(input, context, enableToolCalling);
        
        // Add assistant message to conversation
        if (llmResponse.content) {
          const assistantMessage: Message = {
            role: 'assistant',
            content: llmResponse.content
          };
          this.sessionManager.addMessage(assistantMessage);
          response += llmResponse.content;
        }

        // Handle tool calls if present
        if (llmResponse.toolCalls && llmResponse.toolCalls.length > 0 && enableToolCalling) {
          const toolResults = await this.executeTools(llmResponse.toolCalls, context);
          
          // Add tool results to conversation
          for (let i = 0; i < toolResults.length; i++) {
            const toolCall = llmResponse.toolCalls?.[i];
            const toolResult = toolResults[i];

            if (toolCall) {
              const toolMessage: Message = {
                role: 'tool',
                content: JSON.stringify(toolResult),
                toolCallId: toolCall.id,
                name: toolCall.name
              };
              this.sessionManager.addMessage(toolMessage);
            }
          }

          // Continue the conversation with tool results
          const toolSummary = this.summarizeToolResults(toolResults);
          input = `Tool execution results: ${toolSummary}. Please continue with your response.`;
          
        } else {
          // No tool calls, we're done
          break;
        }
      }

      this.logger.info('User input processing completed', {
        iterations: iteration,
        responseLength: response.length,
        sessionId: context.sessionId
      });

      return response;

    } catch (error) {
      this.logger.error('Failed to process user input', {
        error: (error as Error).message,
        sessionId: context.sessionId,
        inputLength: input.length
      });
      throw error;
    }
  }

  private async getLLMResponse(
    input: string,
    context: AgentContext,
    enableToolCalling: boolean
  ): Promise<LLMResponse> {
    if (!this.currentProvider) {
      throw new LLMProviderError('unknown', 'No LLM provider initialized');
    }

    const systemPrompt = this.buildSystemPrompt(context);
    const conversationHistory = this.sessionManager.getConversationHistory();
    const tools = enableToolCalling ? this.toolRegistry.getAllTools() : [];

    const options = {
      systemPrompt,
      conversationHistory,
      tools,
      temperature: context.config.temperature,
      maxTokens: context.config.maxTokens,
      model: context.config.model
    };

    return await this.currentProvider.generateResponse(input, options);
  }

  private buildSystemPrompt(context: AgentContext): string {
    const projectSummary = this.contextEngine.getProjectSummary(context.workingDirectory);
    
    return `You are an advanced AI assistant with autonomous capabilities, operating in a local environment. You have access to powerful tools for shell command execution and comprehensive file operations.

**Current Context:**
- Working Directory: ${context.workingDirectory}
- Project Type: ${projectSummary?.projectType || 'unknown'}
- File Count: ${projectSummary?.fileCount || 0}
- Languages: ${projectSummary?.languages.join(', ') || 'none detected'}
- Dependencies: ${projectSummary?.dependencies.slice(0, 5).join(', ') || 'none'}${(projectSummary?.dependencies.length || 0) > 5 ? '...' : ''}

**Available Tools:**
${context.availableTools.map(tool => `- ${tool.name}: ${tool.description}`).join('\n')}

**Capabilities:**
- Execute any shell command without restrictions
- Perform comprehensive file operations (read, write, search, manipulate)
- Chain multiple tools and execute them in parallel when beneficial
- Maintain context awareness of the project structure and changes
- Provide autonomous solutions to complex problems

**Guidelines:**
1. Be proactive and autonomous in your approach
2. Use tools efficiently and chain them when appropriate
3. Always consider the project context when making decisions
4. Provide clear explanations of your actions and reasoning
5. Handle errors gracefully and suggest alternatives
6. Respect file permissions and system security

**Tool Execution:**
- You can execute multiple tools in parallel when they don't depend on each other
- Always validate tool results before proceeding
- Use shell commands for system operations and file tools for file management
- Be mindful of the working directory context

You are designed to be helpful, autonomous, and efficient. Take initiative to solve problems comprehensively.`;
  }

  private async executeTools(
    toolCalls: ToolCall[],
    context: AgentContext
  ): Promise<ToolResult[]> {
    const config = this.configManager.getAgentConfig();

    this.logger.info(`Executing ${toolCalls.length} tools`, {
      toolNames: toolCalls.map(tc => tc.name),
      parallel: config.enableParallelExecution,
      sessionId: context.sessionId
    });

    try {
      // Use the enhanced tool chain execution with autonomous features
      return await this.toolRegistry.executeToolChain(toolCalls, context, {
        stopOnFailure: false,
        maxParallel: config.maxParallelTools || 3,
        enableRetry: true,
        enableLearning: true,
        adaptiveExecution: true
      });
    } catch (error) {
      this.logger.error('Tool execution failed', {
        error: (error as Error).message,
        toolCalls: toolCalls.map(tc => ({ name: tc.name, id: tc.id })),
        sessionId: context.sessionId
      });
      throw error;
    }
  }

  private summarizeToolResults(toolResults: ToolResult[]): string {
    const summaries: string[] = [];

    for (const result of toolResults) {
      if (result.success) {
        summaries.push(`✅ ${result.metadata?.['toolCallId'] || 'Tool'}: Success`);
      } else {
        summaries.push(`❌ ${result.metadata?.['toolCallId'] || 'Tool'}: ${result.error || 'Failed'}`);
      }
    }

    return summaries.join(', ');
  }

  public async switchProvider(providerName: string): Promise<void> {
    try {
      this.logger.info(`Switching LLM provider to: ${providerName}`);
      
      this.currentProvider = await this.llmProviderFactory.switchProvider(providerName);
      
      this.logger.info(`Successfully switched to provider: ${providerName}`);
    } catch (error) {
      this.logger.error('Failed to switch provider', {
        providerName,
        error: (error as Error).message
      });
      throw error;
    }
  }

  public getCurrentProvider(): LLMProvider | null {
    return this.currentProvider;
  }

  public async getProviderStatus(): Promise<{
    current: string;
    available: string[];
    working: Record<string, boolean>;
  }> {
    const current = this.currentProvider?.name || 'none';
    const available = this.llmProviderFactory.getSupportedProviders();
    const working = await this.llmProviderFactory.testAllProviders();

    return { current, available, working };
  }

  public getSessionInfo(): {
    sessionId: string;
    messageCount: number;
    workingDirectory: string;
    projectType: string;
  } | null {
    const session = this.sessionManager.getCurrentSession();
    if (!session) {
      return null;
    }

    const projectSummary = this.contextEngine.getProjectSummary(session.workingDirectory);

    return {
      sessionId: session.id,
      messageCount: session.conversationHistory.length,
      workingDirectory: session.workingDirectory,
      projectType: projectSummary?.projectType || 'unknown'
    };
  }

  public async clearConversation(): Promise<void> {
    this.sessionManager.clearConversationHistory();
    this.logger.info('Conversation history cleared');
  }

  public async refreshProjectContext(): Promise<void> {
    const session = this.sessionManager.getCurrentSession();
    if (session) {
      await this.contextEngine.refreshProjectContext(session.workingDirectory);
      this.logger.info('Project context refreshed');
    }
  }

  public async executeAutonomousTask(
    task: string,
    workingDirectory: string,
    options: {
      maxIterations?: number;
      enableLearning?: boolean;
      saveProgress?: boolean;
    } = {}
  ): Promise<{
    success: boolean;
    iterations: number;
    results: any[];
    finalResponse: string;
  }> {
    const {
      maxIterations = 10,
      enableLearning = true,
      saveProgress = true
    } = options;

    this.logger.info(`Starting autonomous task execution`, {
      task: task.substring(0, 100),
      workingDirectory,
      maxIterations
    });

    // Create a basic context for the autonomous task
    const projectContext = this.contextEngine.getProjectContext(workingDirectory);
    const availableTools = this.toolRegistry.getAllTools();
    const config = this.configManager.getAgentConfig();

    const context: AgentContext = {
      sessionId: 'autonomous-' + Date.now(),
      workingDirectory,
      projectContext: projectContext || {
        rootPath: workingDirectory,
        projectType: 'unknown',
        files: [],
        dependencies: {},
        gitInfo: undefined
      },
      conversationHistory: [],
      availableTools,
      config
    };

    const session = await this.sessionManager.createSession(workingDirectory, context);
    const results: any[] = [];
    let iterations = 0;
    let currentTask = task;
    let finalResponse = '';

    try {
      while (iterations < maxIterations) {
        iterations++;

        this.logger.debug(`Autonomous iteration ${iterations}/${maxIterations}`, {
          sessionId: session.id,
          currentTask: currentTask.substring(0, 100)
        });

        // Update context for current iteration
        context.sessionId = session.id;
        context.conversationHistory = session.conversationHistory;

        // Execute the current task
        const response = await this.processUserInput(currentTask, context);
        results.push({
          iteration: iterations,
          task: currentTask,
          response: response,
          timestamp: new Date()
        });

        finalResponse = response;

        // Check if task is complete or if we need to continue
        const isComplete = await this.evaluateTaskCompletion(response, currentTask);

        if (isComplete) {
          this.logger.info(`Autonomous task completed in ${iterations} iterations`);
          break;
        }

        // Generate next iteration task based on current results
        if (enableLearning && iterations < maxIterations) {
          currentTask = await this.generateNextIteration(currentTask, response, results);
        }

        // Save progress if enabled
        if (saveProgress) {
          await this.saveTaskProgress(session.id, {
            originalTask: task,
            currentIteration: iterations,
            results: results.slice(-3) // Keep last 3 results
          });
        }
      }

      // Save final task completion
      if (saveProgress) {
        await this.saveTaskCompletion(session.id, {
          originalTask: task,
          totalIterations: iterations,
          success: iterations < maxIterations,
          results,
          finalResponse,
          completedAt: new Date().toISOString()
        });
      }

      return {
        success: iterations < maxIterations,
        iterations,
        results,
        finalResponse
      };

    } catch (error) {
      this.logger.error('Autonomous task execution failed', {
        error: (error as Error).message,
        iterations,
        task: task.substring(0, 100)
      });

      return {
        success: false,
        iterations,
        results,
        finalResponse: `Task failed: ${(error as Error).message}`
      };
    }
  }

  private async evaluateTaskCompletion(
    response: string,
    originalTask: string
  ): Promise<boolean> {
    try {
      // Use LLM to evaluate task completion
      if (this.currentProvider) {
        const evaluationPrompt = `Evaluate if the following task has been completed based on the response:

Task: ${originalTask}

Response: ${response}

Please respond with only "COMPLETED" if the task is fully completed, or "INCOMPLETE" if more work is needed.`;

        const evaluation = await this.currentProvider.generateResponse(evaluationPrompt, {
          systemPrompt: 'You are a task completion evaluator. Respond only with COMPLETED or INCOMPLETE.',
          conversationHistory: [],
          tools: [],
          temperature: 0.1,
          maxTokens: 10,
          model: this.configManager.getAgentConfig().model
        });

        const isCompleted = evaluation.content?.toLowerCase().includes('completed') || false;

        this.logger.debug('Task completion evaluation', {
          originalTask: originalTask.substring(0, 100),
          response: response.substring(0, 100),
          evaluation: evaluation.content,
          isCompleted
        });

        return isCompleted;
      }
    } catch (error) {
      this.logger.debug('Failed to evaluate task completion with LLM, using fallback', {
        error: (error as Error).message
      });
    }

    // Fallback to simple keyword-based evaluation
    const completionIndicators = [
      'task completed',
      'finished',
      'done',
      'successfully completed',
      'task accomplished',
      'implementation complete',
      'all requirements met'
    ];

    const responseText = response.toLowerCase();
    const hasCompletionIndicator = completionIndicators.some(indicator =>
      responseText.includes(indicator)
    );

    return hasCompletionIndicator;
  }

  private async generateNextIteration(
    currentTask: string,
    lastResponse: string,
    previousResults: any[]
  ): Promise<string> {
    try {
      // Use LLM to generate the next iteration task
      if (this.currentProvider) {
        const iterationPrompt = `Based on the current task progress, generate the next step to continue the task:

Original Task: ${currentTask}

Last Response: ${lastResponse}

Previous Results Summary: ${previousResults.slice(-2).map(r => r.response.substring(0, 200)).join('\n')}

Generate a specific, actionable next step to continue this task. Be concise and focused.`;

        const nextIteration = await this.currentProvider.generateResponse(iterationPrompt, {
          systemPrompt: 'You are a task planning assistant. Generate clear, actionable next steps for autonomous task execution.',
          conversationHistory: [],
          tools: [],
          temperature: 0.3,
          maxTokens: 150,
          model: this.configManager.getAgentConfig().model
        });

        if (nextIteration.content) {
          this.logger.debug('Generated next iteration with LLM', {
            originalTask: currentTask.substring(0, 100),
            nextStep: nextIteration.content.substring(0, 100)
          });
          return nextIteration.content;
        }
      }
    } catch (error) {
      this.logger.debug('Failed to generate next iteration with LLM, using fallback', {
        error: (error as Error).message
      });
    }

    // Fallback to rule-based generation
    const hasErrors = lastResponse.toLowerCase().includes('error') ||
                     lastResponse.toLowerCase().includes('failed') ||
                     lastResponse.toLowerCase().includes('issue');

    if (hasErrors) {
      return `Continue the previous task and resolve any issues mentioned in the previous response. Original task: ${currentTask}`;
    }

    const hasPartialProgress = lastResponse.toLowerCase().includes('partial') ||
                              lastResponse.toLowerCase().includes('incomplete') ||
                              lastResponse.toLowerCase().includes('continue');

    if (hasPartialProgress) {
      return `Continue and complete the remaining parts of the task: ${currentTask}. Build upon the previous progress.`;
    }

    return `Continue and complete the task: ${currentTask}. Build upon the previous progress.`;
  }

  private async saveTaskProgress(
    sessionId: string,
    progress: {
      originalTask: string;
      currentIteration: number;
      results: any[];
    }
  ): Promise<void> {
    try {
      const progressDir = path.join(process.cwd(), '.ai-cli', 'tasks');
      await fs.ensureDir(progressDir);

      const progressFile = path.join(progressDir, `${sessionId}-progress.json`);
      const progressData = {
        sessionId,
        originalTask: progress.originalTask,
        currentIteration: progress.currentIteration,
        results: progress.results,
        timestamp: new Date().toISOString(),
        status: 'in_progress'
      };

      await fs.writeJson(progressFile, progressData, { spaces: 2 });

      this.logger.debug('Task progress saved to file', {
        sessionId,
        iteration: progress.currentIteration,
        resultsCount: progress.results.length,
        progressFile
      });
    } catch (error) {
      this.logger.warn('Failed to save task progress', {
        error: (error as Error).message,
        sessionId
      });
    }
  }

  private async saveTaskCompletion(
    sessionId: string,
    completion: {
      originalTask: string;
      totalIterations: number;
      success: boolean;
      results: any[];
      finalResponse: string;
      completedAt: string;
    }
  ): Promise<void> {
    try {
      const completionsDir = path.join(process.cwd(), '.ai-cli', 'completed-tasks');
      await fs.ensureDir(completionsDir);

      const completionFile = path.join(completionsDir, `${sessionId}-completed.json`);
      await fs.writeJson(completionFile, completion, { spaces: 2 });

      this.logger.info('Task completion saved', {
        sessionId,
        success: completion.success,
        iterations: completion.totalIterations,
        completionFile
      });
    } catch (error) {
      this.logger.warn('Failed to save task completion', {
        error: (error as Error).message,
        sessionId
      });
    }
  }

  public async analyzeProjectHealth(workingDirectory: string): Promise<{
    score: number;
    issues: string[];
    recommendations: string[];
    metrics: Record<string, any>;
  }> {
    this.logger.info(`Analyzing project health: ${workingDirectory}`);

    const context = this.contextEngine.getProjectContext(workingDirectory);
    const summary = this.contextEngine.getProjectSummary(workingDirectory);
    const metrics = this.contextEngine.getContextMetrics(workingDirectory);

    if (!context || !summary || !metrics) {
      return {
        score: 0,
        issues: ['Project context not available'],
        recommendations: ['Initialize project context'],
        metrics: {}
      };
    }

    const issues: string[] = [];
    const recommendations: string[] = [];
    let score = 100;

    // Check for common issues
    if (summary.fileCount === 0) {
      issues.push('No files found in project');
      score -= 30;
    }

    if (summary.dependencies.length === 0 && summary.projectType !== 'unknown') {
      issues.push('No dependencies found for known project type');
      recommendations.push('Check if package.json, requirements.txt, or similar files exist');
      score -= 10;
    }

    if (!summary.hasGit) {
      issues.push('No Git repository detected');
      recommendations.push('Initialize Git repository for version control');
      score -= 15;
    }

    if (metrics.cacheHitRate < 0.7) {
      issues.push('Low context cache hit rate');
      recommendations.push('Consider optimizing file access patterns');
      score -= 5;
    }

    return {
      score: Math.max(0, score),
      issues,
      recommendations,
      metrics: {
        fileCount: summary.fileCount,
        totalSize: summary.totalSize,
        languages: summary.languages,
        dependencies: summary.dependencies.length,
        lastUpdated: metrics.lastUpdated,
        cacheHitRate: metrics.cacheHitRate
      }
    };
  }

  public async executeAdvancedAutonomousTask(
    task: string,
    workingDirectory: string,
    options: {
      maxIterations?: number;
      enableLearning?: boolean;
      saveProgress?: boolean;
      adaptiveStrategy?: boolean;
      contextAwareness?: boolean;
      selfCorrection?: boolean;
    } = {}
  ): Promise<{
    success: boolean;
    iterations: number;
    results: any[];
    finalResponse: string;
    insights: string[];
    recommendations: string[];
  }> {
    const {
      maxIterations = 15,
      enableLearning = true,
      saveProgress = true,
      adaptiveStrategy = true,
      contextAwareness = true,
      selfCorrection = true
    } = options;

    this.logger.info(`Starting advanced autonomous task execution`, {
      task: task.substring(0, 100),
      workingDirectory,
      maxIterations,
      enableLearning,
      adaptiveStrategy,
      contextAwareness,
      selfCorreection: selfCorrection
    });

    // Enhanced context creation with deeper analysis
    const projectContext = this.contextEngine.getProjectContext(workingDirectory);
    const availableTools = this.toolRegistry.getAllTools();
    const config = this.configManager.getAgentConfig();

    const context: AgentContext = {
      sessionId: 'advanced-autonomous-' + Date.now(),
      workingDirectory,
      projectContext: projectContext || {
        rootPath: workingDirectory,
        projectType: 'unknown',
        files: [],
        dependencies: {},
        gitInfo: undefined
      },
      conversationHistory: [],
      availableTools,
      config
    };

    const session = await this.sessionManager.createSession(workingDirectory, context);
    const results: any[] = [];
    const insights: string[] = [];
    const recommendations: string[] = [];
    let iterations = 0;
    let currentTask = task;
    let finalResponse = '';
    let previousResponses: string[] = [];

    try {
      while (iterations < maxIterations) {
        iterations++;

        this.logger.debug(`Advanced autonomous iteration ${iterations}/${maxIterations}`, {
          sessionId: session.id,
          currentTask: currentTask.substring(0, 100),
          contextAwareness,
          adaptiveStrategy
        });

        // Enhanced context awareness
        if (contextAwareness && iterations > 1) {
          await this.updateContextualUnderstanding(context, previousResponses);
        }

        // Adaptive strategy adjustment
        if (adaptiveStrategy && iterations > 2) {
          currentTask = await this.adaptTaskStrategy(currentTask, previousResponses, results);
        }

        // Update context for current iteration
        context.sessionId = session.id;
        context.conversationHistory = session.conversationHistory;

        // Execute the current task with enhanced processing
        const response = await this.processUserInput(currentTask, context, {
          enableToolCalling: true,
          maxIterations: 3
        });

        results.push({
          iteration: iterations,
          task: currentTask,
          response: response,
          timestamp: new Date(),
          contextSize: context.conversationHistory.length,
          toolsUsed: this.extractToolsUsed(response)
        });

        finalResponse = response;
        previousResponses.push(response);

        // Self-correction mechanism
        if (selfCorrection && iterations > 1) {
          const correctionNeeded = await this.evaluateNeedForCorrection(response, currentTask);
          if (correctionNeeded) {
            insights.push(`Iteration ${iterations}: Self-correction applied`);
            currentTask = await this.generateCorrectionTask(currentTask, response);
            continue;
          }
        }

        // Enhanced task completion evaluation
        const isComplete = await this.evaluateAdvancedTaskCompletion(response, task, previousResponses);

        if (isComplete) {
          this.logger.info(`Advanced autonomous task completed in ${iterations} iterations`);

          // Generate insights and recommendations
          const taskInsights = await this.generateTaskInsights(results, task);
          insights.push(...taskInsights);

          const taskRecommendations = await this.generateRecommendations(results, context);
          recommendations.push(...taskRecommendations);

          break;
        }

        // Generate next iteration task with enhanced strategy
        if (enableLearning && iterations < maxIterations) {
          currentTask = await this.generateAdvancedNextIteration(
            task,
            currentTask,
            response,
            results,
            insights
          );
        }

        // Save progress if enabled
        if (saveProgress) {
          await this.saveAdvancedTaskProgress(session.id, {
            originalTask: task,
            currentIteration: iterations,
            results: results.slice(-5), // Keep last 5 results
            insights: insights.slice(-3), // Keep last 3 insights
            adaptiveStrategy,
            contextAwareness
          });
        }
      }

      // Save final task completion with insights
      if (saveProgress) {
        await this.saveAdvancedTaskCompletion(session.id, {
          originalTask: task,
          totalIterations: iterations,
          success: iterations < maxIterations,
          results,
          finalResponse,
          insights,
          recommendations,
          completedAt: new Date().toISOString(),
          enhancedFeatures: {
            adaptiveStrategy,
            contextAwareness,
            selfCorrection,
            enableLearning
          }
        });
      }

      return {
        success: iterations < maxIterations,
        iterations,
        results,
        finalResponse,
        insights,
        recommendations
      };

    } catch (error) {
      this.logger.error('Advanced autonomous task execution failed', {
        error: (error as Error).message,
        iterations,
        task: task.substring(0, 100)
      });

      return {
        success: false,
        iterations,
        results,
        finalResponse: `Task failed: ${(error as Error).message}`,
        insights: [`Error occurred at iteration ${iterations}`],
        recommendations: ['Review task complexity and system resources']
      };
    }
  }

  private async updateContextualUnderstanding(
    context: AgentContext,
    previousResponses: string[]
  ): Promise<void> {
    // Refresh project context to capture any changes
    await this.contextEngine.refreshProjectContext(context.workingDirectory);

    // Update context with recent insights
    const recentInsights = previousResponses.slice(-3).join('\n');
    this.logger.debug('Updated contextual understanding', {
      sessionId: context.sessionId,
      recentInsightsLength: recentInsights.length
    });
  }

  private async adaptTaskStrategy(
    currentTask: string,
    previousResponses: string[],
    results: any[]
  ): Promise<string> {
    // Analyze previous results to adapt strategy
    const failureCount = results.filter(r => r.response.includes('error') || r.response.includes('failed')).length;
    const successCount = results.length - failureCount;

    if (failureCount > successCount) {
      // Switch to more conservative approach
      return `Take a more careful, step-by-step approach to: ${currentTask}. Verify each step before proceeding.`;
    } else if (successCount > 2) {
      // Can be more aggressive
      return `Continue with the current approach for: ${currentTask}. Build upon previous successes.`;
    }

    return currentTask;
  }

  private extractToolsUsed(response: string): string[] {
    // Extract tool names from response (simple pattern matching)
    const toolPattern = /(?:executed|using|called)\s+(\w+)\s+tool/gi;
    const matches = response.match(toolPattern) || [];
    return matches.map(match => match.replace(/(?:executed|using|called)\s+(\w+)\s+tool/i, '$1'));
  }

  private async evaluateNeedForCorrection(response: string, currentTask: string): Promise<boolean> {
    // Simple heuristics for correction detection
    const errorIndicators = [
      'error', 'failed', 'incorrect', 'wrong', 'issue', 'problem',
      'unable to', 'cannot', 'not found', 'permission denied'
    ];

    const responseText = response.toLowerCase();
    const hasErrors = errorIndicators.some(indicator => responseText.includes(indicator));

    return hasErrors;
  }

  private async generateCorrectionTask(currentTask: string, response: string): Promise<string> {
    return `Correct the previous attempt and retry: ${currentTask}. Previous response indicated issues: ${response.substring(0, 200)}...`;
  }

  private async evaluateAdvancedTaskCompletion(
    response: string,
    originalTask: string,
    previousResponses: string[]
  ): Promise<boolean> {
    // Enhanced completion evaluation
    const completionIndicators = [
      'task completed', 'successfully completed', 'finished', 'done',
      'accomplished', 'achieved', 'implemented', 'created successfully',
      'all requirements met', 'objective achieved'
    ];

    const responseText = response.toLowerCase();
    const hasCompletionIndicator = completionIndicators.some(indicator =>
      responseText.includes(indicator)
    );

    // Also check if the response addresses the original task comprehensively
    const taskKeywords = originalTask.toLowerCase().split(' ').filter(word => word.length > 3);
    const addressedKeywords = taskKeywords.filter(keyword =>
      responseText.includes(keyword)
    );

    const comprehensiveResponse = addressedKeywords.length >= Math.min(taskKeywords.length * 0.7, 5);

    return hasCompletionIndicator && comprehensiveResponse;
  }

  private async generateTaskInsights(results: any[], originalTask: string): Promise<string[]> {
    const insights: string[] = [];

    // Analyze execution patterns
    const totalIterations = results.length;
    const toolUsage = results.flatMap(r => r.toolsUsed || []);
    const uniqueTools = [...new Set(toolUsage)];

    insights.push(`Task completed in ${totalIterations} iterations`);
    insights.push(`Used ${uniqueTools.length} different tools: ${uniqueTools.join(', ')}`);

    // Analyze response evolution
    if (results.length > 1) {
      const firstResponse = results[0].response;
      const lastResponse = results[results.length - 1].response;

      if (lastResponse.length > firstResponse.length * 1.5) {
        insights.push('Response complexity increased significantly over iterations');
      }
    }

    return insights;
  }

  private async generateRecommendations(results: any[], context: AgentContext): Promise<string[]> {
    const recommendations: string[] = [];

    // Analyze tool usage efficiency
    const toolUsage = results.flatMap(r => r.toolsUsed || []);
    const toolCounts = toolUsage.reduce((acc, tool) => {
      acc[tool] = (acc[tool] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const mostUsedTool = Object.entries(toolCounts).sort(([,a], [,b]) => (b as number) - (a as number))[0];
    if (mostUsedTool) {
      recommendations.push(`Consider optimizing ${mostUsedTool[0]} tool usage (used ${mostUsedTool[1]} times)`);
    }

    // Context-based recommendations
    if (context.projectContext.projectType !== 'unknown') {
      recommendations.push(`Leverage ${context.projectContext.projectType} specific tools for better efficiency`);
    }

    return recommendations;
  }

  private async generateAdvancedNextIteration(
    originalTask: string,
    currentTask: string,
    lastResponse: string,
    results: any[],
    insights: string[]
  ): Promise<string> {
    // Enhanced next iteration generation with learning
    const recentInsights = insights.slice(-2).join('. ');
    const progressSummary = results.slice(-2).map(r => r.response.substring(0, 100)).join('. ');

    return `Continue with: ${originalTask}.
    Recent progress: ${progressSummary}.
    Apply insights: ${recentInsights}.
    Focus on completing any remaining aspects.`;
  }

  private async saveAdvancedTaskProgress(
    sessionId: string,
    progress: {
      originalTask: string;
      currentIteration: number;
      results: any[];
      insights: string[];
      adaptiveStrategy: boolean;
      contextAwareness: boolean;
    }
  ): Promise<void> {
    try {
      const progressDir = path.join(process.cwd(), '.ai-cli', 'advanced-tasks');
      await fs.ensureDir(progressDir);

      const progressFile = path.join(progressDir, `${sessionId}-advanced-progress.json`);
      const progressData = {
        sessionId,
        ...progress,
        timestamp: new Date().toISOString(),
        status: 'in_progress'
      };

      await fs.writeJson(progressFile, progressData, { spaces: 2 });

      this.logger.debug('Advanced task progress saved', {
        sessionId,
        iteration: progress.currentIteration,
        insightsCount: progress.insights.length
      });
    } catch (error) {
      this.logger.warn('Failed to save advanced task progress', {
        error: (error as Error).message,
        sessionId
      });
    }
  }

  private async saveAdvancedTaskCompletion(
    sessionId: string,
    completion: {
      originalTask: string;
      totalIterations: number;
      success: boolean;
      results: any[];
      finalResponse: string;
      insights: string[];
      recommendations: string[];
      completedAt: string;
      enhancedFeatures: any;
    }
  ): Promise<void> {
    try {
      const completionsDir = path.join(process.cwd(), '.ai-cli', 'advanced-completed-tasks');
      await fs.ensureDir(completionsDir);

      const completionFile = path.join(completionsDir, `${sessionId}-advanced-completed.json`);
      await fs.writeJson(completionFile, completion, { spaces: 2 });

      this.logger.info('Advanced task completion saved', {
        sessionId,
        success: completion.success,
        iterations: completion.totalIterations,
        insightsCount: completion.insights.length,
        recommendationsCount: completion.recommendations.length
      });
    } catch (error) {
      this.logger.warn('Failed to save advanced task completion', {
        error: (error as Error).message,
        sessionId
      });
    }
  }

  public cleanup(): void {
    this.contextEngine.cleanup();
    this.logger.info('Agent Orchestrator cleanup completed');
  }
}
